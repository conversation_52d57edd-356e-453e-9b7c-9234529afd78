{"rustc": 1842507548689473721, "features": "[\"http1\", \"http2\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 15657897354478470176, "path": 4951476041381476177, "deps": [[41016358116313498, "hyper_util", false, 15643412824638214917], [784494742817713399, "tower_service", false, 16624469884409363443], [1542112352204983347, "rustls", false, 4006092481022461024], [2883436298747778685, "pki_types", false, 12426637349468929886], [8153991275959898788, "webpki_roots", false, 6405735322127704543], [9010263965687315507, "http", false, 7590422185820555865], [11895591994124935963, "tokio_rustls", false, 6588884352694830292], [11957360342995674422, "hyper", false, 17924745426058331237], [17531218394775549125, "tokio", false, 9577098814723763327]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-rustls-9d8cfaa1555d9786\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}