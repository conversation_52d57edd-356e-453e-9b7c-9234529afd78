{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 13895037601511520678, "deps": [[654232091421095663, "tauri_utils", false, 16061983275688939768], [1140993874832966859, "tauri_winres", false, 3079751992139394837], [4824857623768494398, "cargo_toml", false, 9436977402825865820], [4899080583175475170, "semver", false, 9066065772989538815], [6913375703034175521, "schemars", false, 10184923136302597262], [7170110829644101142, "json_patch", false, 1935429092610753451], [9689903380558560274, "serde", false, 1463083818036528110], [13077543566650298139, "heck", false, 10697231594949197088], [13625485746686963219, "anyhow", false, 13382587763378568758], [15609422047640926750, "toml", false, 16004562203480463698], [15622660310229662834, "walkdir", false, 5317099026600890234], [16362055519698394275, "serde_json", false, 7905622455244592243], [16928111194414003569, "dirs", false, 7351795522596350183], [17155886227862585100, "glob", false, 16486553695450081982]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-e695c901ea47f479\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}