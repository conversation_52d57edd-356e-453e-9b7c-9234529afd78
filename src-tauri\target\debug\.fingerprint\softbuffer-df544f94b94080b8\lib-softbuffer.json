{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 12626937456420643737, "deps": [[376837177317575824, "build_script_build", false, 16147511210383637284], [4143744114649553716, "raw_window_handle", false, 4338345533142928864], [5986029879202738730, "log", false, 1383228523472180846], [10281541584571964250, "windows_sys", false, 15231939577342712027]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-df544f94b94080b8\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}