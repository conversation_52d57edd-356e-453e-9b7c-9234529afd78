{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 5764716325001790305, "deps": [[654232091421095663, "tauri_utils", false, 7240725375555234430], [3150220818285335163, "url", false, 12545958362098941681], [4143744114649553716, "raw_window_handle", false, 4338345533142928864], [7606335748176206944, "dpi", false, 16931135009390995988], [9010263965687315507, "http", false, 7590422185820555865], [9689903380558560274, "serde", false, 4485493638115277502], [10806645703491011684, "thiserror", false, 14351478128591636879], [12943761728066819757, "build_script_build", false, 9819658407239896953], [14585479307175734061, "windows", false, 18071829684179963176], [16362055519698394275, "serde_json", false, 8308832370002487686], [16727543399706004146, "cookie", false, 12028417081850931268]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-51769dde7ca99d75\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}