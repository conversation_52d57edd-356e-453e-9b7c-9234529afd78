{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 15657897354478470176, "path": 14661090343348636803, "deps": [[4972584477725338812, "build_script_build", false, 7340151915156326578], [5986029879202738730, "log", false, 1383228523472180846], [9451456094439810778, "regex", false, 9373537035660870258], [9689903380558560274, "serde", false, 4485493638115277502], [10806645703491011684, "thiserror", false, 14351478128591636879], [11337703028400419576, "os_pipe", false, 11382884626046505738], [12092653563678505622, "tauri", false, 10734534781098557353], [14564311161534545801, "encoding_rs", false, 17598767583244058691], [15722096100444777195, "shared_child", false, 216410419543217547], [16192041687293812804, "open", false, 7655746454329638200], [16362055519698394275, "serde_json", false, 8308832370002487686], [17531218394775549125, "tokio", false, 9577098814723763327]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-shell-16614d0df94fb821\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}