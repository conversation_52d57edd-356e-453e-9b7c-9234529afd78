{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 12517076980388218495, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 67954487117658868], [3150220818285335163, "url", false, 12545958362098941681], [3191507132440681679, "serde_untagged", false, 11409281165603641627], [4071963112282141418, "serde_with", false, 17156749796843859220], [4899080583175475170, "semver", false, 8573224447005181808], [5986029879202738730, "log", false, 1383228523472180846], [6606131838865521726, "ctor", false, 8880461044427089760], [7170110829644101142, "json_patch", false, 15838342227136607725], [8319709847752024821, "uuid", false, 8732291738522461133], [9010263965687315507, "http", false, 7590422185820555865], [9451456094439810778, "regex", false, 9373537035660870258], [9556762810601084293, "brotli", false, 14682661840420589503], [9689903380558560274, "serde", false, 4485493638115277502], [10806645703491011684, "thiserror", false, 14351478128591636879], [11989259058781683633, "dunce", false, 415105493531948346], [13625485746686963219, "anyhow", false, 13382587763378568758], [15609422047640926750, "toml", false, 5170934707600863503], [15622660310229662834, "walkdir", false, 14871353804785462141], [15932120279885307830, "memchr", false, 13396250273569789704], [16362055519698394275, "serde_json", false, 8308832370002487686], [17146114186171651583, "infer", false, 1772709792138530376], [17155886227862585100, "glob", false, 16486553695450081982], [17186037756130803222, "phf", false, 4196621628720324083]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-7702e88f2bea3660\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}