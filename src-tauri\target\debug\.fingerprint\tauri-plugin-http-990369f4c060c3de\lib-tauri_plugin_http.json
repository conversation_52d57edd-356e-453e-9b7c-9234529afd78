{"rustc": 1842507548689473721, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 15657897354478470176, "path": 6256446883745084329, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 67954487117658868], [3150220818285335163, "url", false, 12545958362098941681], [7085222851776090619, "reqwest", false, 15553427121819746626], [8298091525883606470, "cookie_store", false, 7223463099211851302], [9010263965687315507, "http", false, 7590422185820555865], [9451456094439810778, "regex", false, 9373537035660870258], [9689903380558560274, "serde", false, 4485493638115277502], [10806645703491011684, "thiserror", false, 14351478128591636879], [12092653563678505622, "tauri", false, 10734534781098557353], [12504415026414629397, "tauri_plugin_fs", false, 11189904118934755714], [14909000976169095833, "build_script_build", false, 5796491446307976521], [16066129441945555748, "bytes", false, 4964250633549122261], [16362055519698394275, "serde_json", false, 8308832370002487686], [17047088963840213854, "data_url", false, 13718393740895661208], [17531218394775549125, "tokio", false, 9577098814723763327]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-990369f4c060c3de\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}