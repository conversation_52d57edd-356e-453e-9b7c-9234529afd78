// Learn more about <PERSON>ri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
async fn navigate_to_url(app: tauri::AppHandle, url: String) -> Result<(), String> {
    use tauri::Manager;

    // 获取主窗口
    let window = app.get_webview_window("main").ok_or("Main window not found")?;

    // 使用 eval 在当前窗口中导航
    let script = format!("window.location.href = '{}'", url);
    window.eval(&script).map_err(|e| format!("Failed to navigate: {}", e))?;

    Ok(())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_http::init())
        .invoke_handler(tauri::generate_handler![greet, navigate_to_url])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
