{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 14015810439940011942, "deps": [[40386456601120721, "percent_encoding", false, 16222558833392446400], [654232091421095663, "tauri_utils", false, 7240725375555234430], [1200537532907108615, "url<PERSON><PERSON>n", false, 67954487117658868], [1967864351173319501, "muda", false, 12094217810003599964], [2013030631243296465, "webview2_com", false, 12453090559474807160], [3150220818285335163, "url", false, 12545958362098941681], [3331586631144870129, "getrandom", false, 9839287831678168304], [4143744114649553716, "raw_window_handle", false, 4338345533142928864], [4919829919303820331, "serialize_to_javascript", false, 13998862736855440433], [5986029879202738730, "log", false, 1383228523472180846], [9010263965687315507, "http", false, 7590422185820555865], [9689903380558560274, "serde", false, 4485493638115277502], [10229185211513642314, "mime", false, 15352488344281205559], [10806645703491011684, "thiserror", false, 14351478128591636879], [11989259058781683633, "dunce", false, 415105493531948346], [12092653563678505622, "build_script_build", false, 2168613222266226633], [12304025191202589669, "tauri_runtime_wry", false, 8900764489036575976], [12565293087094287914, "window_vibrancy", false, 2828644616406175479], [12943761728066819757, "tauri_runtime", false, 529457141630468196], [12986574360607194341, "serde_repr", false, 17609191736288973006], [13077543566650298139, "heck", false, 10697231594949197088], [13405681745520956630, "tauri_macros", false, 14557061306345483202], [13625485746686963219, "anyhow", false, 13382587763378568758], [14585479307175734061, "windows", false, 18071829684179963176], [16362055519698394275, "serde_json", false, 8308832370002487686], [16928111194414003569, "dirs", false, 5362989902883408975], [17155886227862585100, "glob", false, 16486553695450081982], [17531218394775549125, "tokio", false, 9577098814723763327]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-9c13235908e30465\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}