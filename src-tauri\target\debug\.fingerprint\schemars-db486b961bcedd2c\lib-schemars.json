{"rustc": 1842507548689473721, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 2225463790103693989, "path": 10055452014528492316, "deps": [[3150220818285335163, "url", false, 12297339822464630731], [6913375703034175521, "build_script_build", false, 3574317029152452664], [6982418085031928086, "dyn_clone", false, 2207851045947108694], [8319709847752024821, "uuid1", false, 10787607961870723040], [9689903380558560274, "serde", false, 1463083818036528110], [14923790796823607459, "indexmap", false, 12693825861960769325], [16071897500792579091, "schemars_derive", false, 11203856388153740399], [16362055519698394275, "serde_json", false, 7905622455244592243]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\schemars-db486b961bcedd2c\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}