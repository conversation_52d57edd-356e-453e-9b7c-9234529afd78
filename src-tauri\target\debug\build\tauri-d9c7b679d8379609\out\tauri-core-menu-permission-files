["\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\menu\\autogenerated\\default.toml"]