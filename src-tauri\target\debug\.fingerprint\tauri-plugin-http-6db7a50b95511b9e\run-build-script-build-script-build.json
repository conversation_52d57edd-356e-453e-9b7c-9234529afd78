{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12092653563678505622, "build_script_build", false, 2168613222266226633], [12504415026414629397, "build_script_build", false, 11389383867724396997], [14909000976169095833, "build_script_build", false, 8878056986699220340]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri-plugin-http-6db7a50b95511b9e\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}