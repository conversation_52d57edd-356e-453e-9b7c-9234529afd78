# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-is-closable"
description = "Enables the is_closable command without any pre-configured scope."
commands.allow = ["is_closable"]

[[permission]]
identifier = "deny-is-closable"
description = "Denies the is_closable command without any pre-configured scope."
commands.deny = ["is_closable"]
