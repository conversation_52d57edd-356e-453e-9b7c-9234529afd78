{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12092653563678505622, "build_script_build", false, 5762207606563803124], [16702348383442838006, "build_script_build", false, 8098412190102872906], [6800327204153422865, "build_script_build", false, 10335986751723114051]], "local": [{"RerunIfChanged": {"output": "debug\\build\\xm-d385765d79fbfcd8\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}