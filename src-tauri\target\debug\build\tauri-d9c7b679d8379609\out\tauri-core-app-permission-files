["\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\app\\autogenerated\\commands\\app_hide.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\app\\autogenerated\\commands\\app_show.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\app\\autogenerated\\commands\\bundle_type.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\app\\autogenerated\\commands\\default_window_icon.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\app\\autogenerated\\commands\\fetch_data_store_identifiers.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\app\\autogenerated\\commands\\identifier.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\app\\autogenerated\\commands\\name.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\app\\autogenerated\\commands\\remove_data_store.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\app\\autogenerated\\commands\\set_app_theme.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\app\\autogenerated\\commands\\set_dock_visibility.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\app\\autogenerated\\commands\\tauri_version.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\app\\autogenerated\\commands\\version.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\app\\autogenerated\\default.toml"]