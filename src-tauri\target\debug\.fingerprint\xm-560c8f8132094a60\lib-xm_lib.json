{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 10488095019656441081, "profile": 8731458305071235362, "path": 10763286916239946207, "deps": [[4972584477725338812, "tauri_plugin_shell", false, 7286807966255890889], [6800327204153422865, "build_script_build", false, 12881493074543003159], [9689903380558560274, "serde", false, 4485493638115277502], [12092653563678505622, "tauri", false, 10734534781098557353], [12504415026414629397, "tauri_plugin_fs", false, 11189904118934755714], [14909000976169095833, "tauri_plugin_http", false, 16527133692670193854], [16362055519698394275, "serde_json", false, 8308832370002487686], [16702348383442838006, "tauri_plugin_opener", false, 9948146575849669141]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\xm-560c8f8132094a60\\dep-lib-xm_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}