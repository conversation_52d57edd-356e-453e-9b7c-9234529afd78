{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 3776814082932214871, "deps": [[654232091421095663, "tauri_utils", false, 16061983275688939768], [3060637413840920116, "proc_macro2", false, 3196051902200663950], [3150220818285335163, "url", false, 12297339822464630731], [4899080583175475170, "semver", false, 9066065772989538815], [4974441333307933176, "syn", false, 12573928928994442457], [7170110829644101142, "json_patch", false, 1935429092610753451], [7392050791754369441, "ico", false, 15277375546079946197], [8319709847752024821, "uuid", false, 10787607961870723040], [9556762810601084293, "brotli", false, 14682661840420589503], [9689903380558560274, "serde", false, 1463083818036528110], [9857275760291862238, "sha2", false, 9473992456333241692], [10806645703491011684, "thiserror", false, 14351478128591636879], [12687914511023397207, "png", false, 15938791281193582863], [13077212702700853852, "base64", false, 14661201377418349651], [15622660310229662834, "walkdir", false, 5317099026600890234], [16362055519698394275, "serde_json", false, 7905622455244592243], [17990358020177143287, "quote", false, 2963802732501907807]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-631c34a02386308e\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}