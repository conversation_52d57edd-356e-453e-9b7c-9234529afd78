{"rustc": 1842507548689473721, "features": "[\"follow-redirect\", \"futures-util\", \"iri-string\", \"tower\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"futures-core\", \"futures-util\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 17577061573142048237, "profile": 15657897354478470176, "path": 10227123107793502362, "deps": [[784494742817713399, "tower_service", false, 16624469884409363443], [1906322745568073236, "pin_project_lite", false, 8741015303972118796], [4121350475192885151, "iri_string", false, 16668931919782007168], [5695049318159433696, "tower", false, 7406672505895548936], [7712452662827335977, "tower_layer", false, 13650399192265926004], [7896293946984509699, "bitflags", false, 2343347456972401724], [9010263965687315507, "http", false, 7590422185820555865], [10629569228670356391, "futures_util", false, 9450682545621662163], [14084095096285906100, "http_body", false, 16342905373675904739], [16066129441945555748, "bytes", false, 4964250633549122261]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tower-http-f0cb2815e241061e\\dep-lib-tower_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}