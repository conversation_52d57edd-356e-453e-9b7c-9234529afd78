["\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\D:\\office\\cursor\\xm\\src-tauri\\target\\debug\\build\\tauri-d9c7b679d8379609\\out\\permissions\\path\\autogenerated\\default.toml"]