{"$schema": "https://schema.tauri.app/config/2", "productName": "xm", "version": "0.1.0", "identifier": "com.xm.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "小米微服务", "width": 1280, "height": 720, "resizable": true, "minimizable": true, "maximizable": true, "closable": true}], "security": {"csp": null, "dangerousDisableAssetCspModification": true}, "withGlobalTauri": false}, "plugins": {"shell": {"open": true}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}