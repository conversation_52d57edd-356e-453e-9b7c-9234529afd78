{"$schema": "https://schema.tauri.app/config/2", "productName": "xm", "version": "0.1.0", "identifier": "com.xm.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "小米微服务", "width": 1280, "height": 720, "resizable": true, "minimizable": true, "maximizable": true, "closable": true}], "security": {"csp": null}}, "plugins": {"shell": {"open": true}, "fs": {"readFile": true, "writeFile": true, "readDir": true, "copyFile": true, "createDir": true, "removeDir": true, "removeFile": true, "renameFile": true}, "http": {"request": true}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}