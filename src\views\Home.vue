<template>
  <main class="container">
    <div class="header">
      <div class="header-left">
        <img src="/xiaomi-logo.svg" class="logo xiaomi" alt="小米 logo" />
        <h1>小米微服务</h1>
      </div>
      <div class="nav-controls">
        <button @click="goBack" :disabled="!canGoBack" class="nav-btn">← 后退</button>
        <button @click="goForward" :disabled="!canGoForward" class="nav-btn">前进 →</button>
        <button @click="refresh" class="nav-btn">刷新</button>
        <button @click="goHome" class="nav-btn">首页</button>
      </div>
    </div>

    <div class="url-bar">
      <span class="url-text">{{ currentUrl }}</span>
    </div>

    <div class="content-frame">
      <iframe
        ref="iframeRef"
        :src="currentUrl"
        class="xiaomi-iframe"
        frameborder="0"
        allowfullscreen
        @load="onIframeLoad"
        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-top-navigation"
      ></iframe>
    </div>

    <div class="loading" v-if="isLoading">
      <p>正在加载小米官网内容...</p>
      <div class="loading-spinner"></div>
    </div>
  </main>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";

const isLoading = ref(true);
const currentUrl = ref("https://www.xiaomi.cn/board");
const canGoBack = ref(false);
const canGoForward = ref(false);
const iframeRef = ref(null);
const history = ref([]);
const historyIndex = ref(-1);

onMounted(async () => {
  // 初始化浏览器
  await navigateToUrl(currentUrl.value);
});

async function navigateToUrl(url) {
  try {
    isLoading.value = true;
    currentUrl.value = url;

    // 添加到历史记录
    if (historyIndex.value === -1 || history.value[historyIndex.value] !== url) {
      // 如果不在历史记录末尾，删除后面的记录
      if (historyIndex.value < history.value.length - 1) {
        history.value = history.value.slice(0, historyIndex.value + 1);
      }
      history.value.push(url);
      historyIndex.value = history.value.length - 1;
    }

    updateNavigationState();

    // 等待 iframe 加载
    await nextTick();

  } catch (error) {
    console.error("导航失败:", error);
    isLoading.value = false;
  }
}

function updateNavigationState() {
  canGoBack.value = historyIndex.value > 0;
  canGoForward.value = historyIndex.value < history.value.length - 1;
}

function goBack() {
  if (canGoBack.value) {
    historyIndex.value--;
    currentUrl.value = history.value[historyIndex.value];
    updateNavigationState();
  }
}

function goForward() {
  if (canGoForward.value) {
    historyIndex.value++;
    currentUrl.value = history.value[historyIndex.value];
    updateNavigationState();
  }
}

function refresh() {
  const iframe = iframeRef.value;
  if (iframe) {
    iframe.src = iframe.src;
  }
}

function goHome() {
  navigateToUrl("https://www.xiaomi.cn/board");
}

function onIframeLoad() {
  isLoading.value = false;

  // 尝试获取 iframe 内的 URL（可能因为跨域限制而失败）
  try {
    const iframe = iframeRef.value;
    if (iframe && iframe.contentWindow) {
      const iframeUrl = iframe.contentWindow.location.href;
      if (iframeUrl && iframeUrl !== 'about:blank') {
        currentUrl.value = iframeUrl;
      }
    }
  } catch (e) {
    // 跨域限制，忽略错误
  }
}
</script>

<style scoped>
.container {
  margin: 0;
  padding: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 20px;
  background-color: #ff6900;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-height: 50px;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo.xiaomi {
  height: 32px;
  margin-right: 12px;
  filter: brightness(0) invert(1);
}

h1 {
  margin: 0;
  font-size: 1.3em;
  font-weight: 500;
}

.nav-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.nav-btn {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.2s ease;
}

.nav-btn:hover:not(:disabled) {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.url-bar {
  background-color: #f8f8f8;
  padding: 8px 20px;
  border-bottom: 1px solid #e0e0e0;
  font-size: 0.9em;
  color: #666;
}

.url-text {
  font-family: monospace;
  word-break: break-all;
}

.content-frame {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.xiaomi-iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}

.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.95);
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  z-index: 10000;
  text-align: center;
  min-width: 200px;
}

.loading p {
  margin: 0 0 15px 0;
  color: #ff6900;
  font-weight: 500;
  font-size: 1.1em;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #ff6900;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }

  .url-bar {
    background-color: #2a2a2a;
    border-bottom-color: #404040;
    color: #ccc;
  }

  .loading {
    background-color: rgba(26, 26, 26, 0.95);
    color: white;
  }

  .loading p {
    color: #ff6900;
  }

  .loading-spinner {
    border-color: #404040;
    border-top-color: #ff6900;
  }
}
</style>
