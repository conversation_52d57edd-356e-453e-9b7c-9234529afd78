<template>
  <main class="container">
    <div class="header">
      <div class="header-left">
        <img src="/xiaomi-logo.svg" class="logo xiaomi" alt="小米 logo" />
        <h1>小米微服务</h1>
      </div>
      <div class="nav-controls">
        <button @click="openBrowser" class="nav-btn primary">🌐 打开小米官网</button>
        <button @click="openMiStore" class="nav-btn">🛒 小米商城</button>
        <button @click="openMiCommunity" class="nav-btn">💬 小米社区</button>
        <button @click="openMiCloud" class="nav-btn">☁️ 小米云服务</button>
        <button @click="goHome" class="nav-btn home-btn">🏠 返回首页</button>
      </div>
    </div>

    <div class="welcome-content">
      <div class="welcome-card">
        <h2>欢迎使用小米微服务浏览器</h2>
        <p>点击上方按钮打开对应的小米服务页面</p>
        <div class="features">
          <div class="feature-item">
            <span class="feature-icon">🚀</span>
            <span class="feature-text">原生浏览器内核</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">🔒</span>
            <span class="feature-text">安全访问保护</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">⚡</span>
            <span class="feature-text">快速响应体验</span>
          </div>
        </div>
      </div>
    </div>

    <div class="loading" v-if="isLoading">
      <p>正在打开浏览器窗口...</p>
      <div class="loading-spinner"></div>
    </div>
  </main>
</template>

<script setup>
import { ref } from "vue";
import { invoke } from "@tauri-apps/api/core";

const isLoading = ref(false);

async function navigateToUrl(url) {
  try {
    isLoading.value = true;
    await invoke('navigate_to_url', { url });
    console.log('导航成功:', url);
  } catch (error) {
    console.error('导航失败:', error);
    alert('导航失败: ' + error);
  } finally {
    isLoading.value = false;
  }
}

async function openBrowser() {
  await navigateToUrl('https://www.xiaomi.cn/board');
}

async function openMiStore() {
  await navigateToUrl('https://www.mi.com/');
}

async function openMiCommunity() {
  await navigateToUrl('https://www.xiaomi.cn/board');
}

async function openMiCloud() {
  await navigateToUrl('https://i.mi.com/');
}

async function goHome() {
  // 返回到应用首页
  window.location.reload();
}
</script>

<style scoped>
.container {
  margin: 0;
  padding: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ff6900 0%, #ff8533 100%);
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 30px;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.logo.xiaomi {
  height: 40px;
  margin-right: 15px;
  filter: brightness(0) invert(1);
}

h1 {
  margin: 0;
  font-size: 1.5em;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.nav-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.nav-btn {
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9em;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.nav-btn:hover {
  background-color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.nav-btn.primary {
  background-color: rgba(255, 255, 255, 0.9);
  color: #ff6900;
  font-weight: 600;
}

.nav-btn.primary:hover {
  background-color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.nav-btn.home-btn {
  background-color: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.4);
}

.nav-btn.home-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.6);
}

.welcome-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.welcome-card {
  background-color: rgba(255, 255, 255, 0.95);
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 600px;
  backdrop-filter: blur(20px);
}

.welcome-card h2 {
  color: #ff6900;
  font-size: 2em;
  margin: 0 0 15px 0;
  font-weight: 600;
}

.welcome-card p {
  color: #666;
  font-size: 1.1em;
  margin: 0 0 30px 0;
  line-height: 1.6;
}

.features {
  display: flex;
  justify-content: space-around;
  gap: 20px;
  margin-top: 30px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.feature-icon {
  font-size: 2em;
  margin-bottom: 5px;
}

.feature-text {
  color: #666;
  font-size: 0.9em;
  font-weight: 500;
}

.loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.95);
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  z-index: 10000;
  text-align: center;
  min-width: 250px;
  backdrop-filter: blur(20px);
}

.loading p {
  margin: 0 0 20px 0;
  color: #ff6900;
  font-weight: 600;
  font-size: 1.1em;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #ff6900;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 15px;
    padding: 20px;
  }

  .nav-controls {
    flex-wrap: wrap;
    justify-content: center;
  }

  .features {
    flex-direction: column;
    gap: 15px;
  }

  .welcome-card {
    padding: 30px 20px;
  }
}

@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #cc5200 0%, #ff6900 100%);
  }

  .welcome-card {
    background-color: rgba(26, 26, 26, 0.95);
  }

  .welcome-card h2 {
    color: #ff8533;
  }

  .welcome-card p,
  .feature-text {
    color: #ccc;
  }

  .loading {
    background-color: rgba(26, 26, 26, 0.95);
  }

  .loading p {
    color: #ff8533;
  }

  .loading-spinner {
    border-color: #404040;
    border-top-color: #ff8533;
  }
}
</style>
