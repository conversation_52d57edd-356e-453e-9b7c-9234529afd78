<template>
  <main class="container">
    <div class="header">
      <img src="/xiaomi-logo.svg" class="logo xiaomi" alt="小米 logo" />
      <h1>小米微服务</h1>
    </div>

    <div class="content-frame">
      <iframe
        src="https://www.xiaomi.cn/board"
        class="xiaomi-iframe"
        frameborder="0"
        allowfullscreen
        @load="onIframeLoad"
      ></iframe>
    </div>

    <div class="loading" v-if="isLoading">
      <p>正在加载小米官网内容...</p>
    </div>
  </main>
</template>

<script setup>
import { ref } from "vue";

const isLoading = ref(true);

function onIframeLoad() {
  isLoading.value = false;
}
</script>

<style scoped>
.container {
  margin: 0;
  padding: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  background-color: #ff6900;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.logo.xiaomi {
  height: 40px;
  margin-right: 15px;
  filter: brightness(0) invert(1);
}

h1 {
  margin: 0;
  font-size: 1.5em;
  font-weight: 500;
}

.content-frame {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.xiaomi-iframe {
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.loading p {
  margin: 0;
  color: #ff6900;
  font-weight: 500;
}

@media (prefers-color-scheme: dark) {
  .container {
    background-color: #1a1a1a;
  }

  .loading {
    background-color: rgba(26, 26, 26, 0.9);
    color: white;
  }

  .loading p {
    color: #ff6900;
  }
}
</style>
