{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 7382125389373463285, "deps": [[12410631062411814511, "cc", false, 11704924026047437537]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-12340372387b627c\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}