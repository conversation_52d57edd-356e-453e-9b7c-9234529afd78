{"rustc": 1842507548689473721, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"cookies\", \"h2\", \"http2\", \"macos-system-configuration\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"system-proxy\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"system-proxy\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 15302557990823967831, "path": 6220401870345865541, "deps": [[40386456601120721, "percent_encoding", false, 16222558833392446400], [41016358116313498, "hyper_util", false, 15643412824638214917], [784494742817713399, "tower_service", false, 16624469884409363443], [1542112352204983347, "rustls", false, 4006092481022461024], [1788832197870803419, "hyper_rustls", false, 3223938701356144885], [1906322745568073236, "pin_project_lite", false, 8741015303972118796], [2054153378684941554, "tower_http", false, 3252529799009031000], [2517136641825875337, "sync_wrapper", false, 2006908388317673540], [2883436298747778685, "rustls_pki_types", false, 12426637349468929886], [3150220818285335163, "url", false, 12545958362098941681], [5695049318159433696, "tower", false, 7406672505895548936], [5986029879202738730, "log", false, 1383228523472180846], [7620660491849607393, "futures_core", false, 16403896573760854906], [8153991275959898788, "webpki_roots", false, 6405735322127704543], [8298091525883606470, "cookie_store", false, 7223463099211851302], [9010263965687315507, "http", false, 7590422185820555865], [9689903380558560274, "serde", false, 4485493638115277502], [10229185211513642314, "mime", false, 15352488344281205559], [11895591994124935963, "tokio_rustls", false, 6588884352694830292], [11957360342995674422, "hyper", false, 17924745426058331237], [13077212702700853852, "base64", false, 14661201377418349651], [14084095096285906100, "http_body", false, 16342905373675904739], [14359893265615549706, "h2", false, 9231460296387777498], [14564311161534545801, "encoding_rs", false, 17598767583244058691], [16066129441945555748, "bytes", false, 4964250633549122261], [16542808166767769916, "serde_urlencoded", false, 15054962208689477873], [16727543399706004146, "cookie_crate", false, 12028417081850931268], [16900715236047033623, "http_body_util", false, 412606975298017142], [17531218394775549125, "tokio", false, 9577098814723763327]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-779090d1df680724\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}