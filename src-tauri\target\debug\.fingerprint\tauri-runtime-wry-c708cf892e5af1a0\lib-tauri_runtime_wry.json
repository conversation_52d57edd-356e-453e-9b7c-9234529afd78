{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 15538429741342377625, "deps": [[376837177317575824, "softbuffer", false, 2146715025566231922], [654232091421095663, "tauri_utils", false, 7240725375555234430], [2013030631243296465, "webview2_com", false, 12453090559474807160], [3150220818285335163, "url", false, 12545958362098941681], [3722963349756955755, "once_cell", false, 123780520211584151], [4143744114649553716, "raw_window_handle", false, 4338345533142928864], [5986029879202738730, "log", false, 1383228523472180846], [8826339825490770380, "tao", false, 4970783769897527705], [9010263965687315507, "http", false, 7590422185820555865], [9141053277961803901, "wry", false, 15973359042075778716], [12304025191202589669, "build_script_build", false, 15143797179616847490], [12943761728066819757, "tauri_runtime", false, 529457141630468196], [14585479307175734061, "windows", false, 18071829684179963176]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-c708cf892e5af1a0\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}