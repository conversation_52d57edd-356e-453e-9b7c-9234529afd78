<template>
  <div class="welcome-guide" v-if="showGuide">
    <!-- 全屏遮罩层 -->
    <div class="overlay" @click.stop></div>
    
    <!-- 引导内容 -->
    <div class="guide-content">
      <!-- Logo区域 -->
      <div class="logo-container" :class="{ 'animate-in': logoVisible }">
        <div class="logo">
          <img src="/xiaomi-logo.svg" alt="小米微服务" class="logo-image" />
        </div>
      </div>
      
      <!-- 打字机文字 -->
      <div class="text-container" :class="{ 'animate-in': textVisible }">
        <h1 class="welcome-text">{{ displayText }}</h1>
        <div class="cursor" :class="{ 'blink': showCursor }">|</div>
      </div>
      
      <!-- 引导按钮 -->
      <div class="button-container" :class="{ 'animate-in': buttonVisible }">
        <button class="guide-button" @click="completeGuide">
          开始使用
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'

const showGuide = ref(true)
const logoVisible = ref(false)
const textVisible = ref(false)
const buttonVisible = ref(false)
const displayText = ref('')
const showCursor = ref(true)

const fullText = '欢迎使用小米微服务'
let typewriterIndex = 0

const emit = defineEmits(['guide-completed'])

// 打字机效果
const typeWriter = () => {
  if (typewriterIndex < fullText.length) {
    displayText.value += fullText.charAt(typewriterIndex)
    typewriterIndex++
    setTimeout(typeWriter, 150) // 每个字符间隔150ms
  } else {
    // 打字完成后显示按钮
    setTimeout(() => {
      buttonVisible.value = true
    }, 500)
  }
}

// 完成引导
const completeGuide = () => {
  // 触发完成事件
  emit('guide-completed')

  // 隐藏引导页面
  showGuide.value = false
}

onMounted(async () => {
  // 动画序列
  await nextTick()

  // 1. 显示logo (500ms后)
  setTimeout(() => {
    logoVisible.value = true
  }, 500)

  // 2. 显示文字容器并开始打字机效果 (1500ms后)
  setTimeout(() => {
    textVisible.value = true
    setTimeout(() => {
      typeWriter()
    }, 300)
  }, 1500)
})
</script>

<style scoped>
.welcome-guide {
  position: fixed;
  top: -30px; /* 向上偏移覆盖标题栏 */
  left: 0;
  width: 100vw;
  height: calc(100vh + 30px); /* 增加高度覆盖标题栏 */
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #11244c;
  cursor: not-allowed;
}

.guide-content {
  position: relative;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  width: 100%;
  max-width: 1200px;
  padding: 2rem;
}

.logo-container {
  margin-bottom: 3rem;
  opacity: 0;
  transform: scale(0.5) translateY(-30px);
  transition: all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.logo-container.animate-in {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.logo {
  width: 120px;
  height: 120px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: float 3s ease-in-out infinite;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo-image {
  width: 120px;
  height: 120px;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.text-container {
  margin-bottom: 3rem;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease-out;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 800px;
  padding: 0 2rem;
}

.text-container.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.welcome-text {
  font-size: 2.2rem;
  font-weight: 300;
  margin: 0;
  letter-spacing: 2px;
  background: linear-gradient(45deg, #fff, #a8edea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  white-space: nowrap;
  text-align: center;
}

.cursor {
  font-size: 2.5rem;
  color: #fff;
  margin-left: 4px;
  animation: blink 1s infinite;
}

.cursor.blink {
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

.button-container {
  opacity: 0;
  transform: translateY(30px) scale(0.9);
  transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.button-container.animate-in {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.guide-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  font-weight: 500;
  border-radius: 50px;
  cursor: pointer;
  box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.guide-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(102, 126, 234, 0.6);
}

.guide-button:active {
  transform: translateY(0);
}

.guide-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.guide-button:hover::before {
  left: 100%;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .welcome-text {
    font-size: 1.8rem;
  }

  .cursor {
    font-size: 1.8rem;
  }
}

@media (max-width: 768px) {
  .welcome-text {
    font-size: 1.5rem;
    letter-spacing: 1px;
  }

  .cursor {
    font-size: 1.5rem;
  }

  .logo {
    width: 100px;
    height: 100px;
  }

  .logo-image {
    width: 100px;
    height: 100px;
  }

  .guide-button {
    padding: 0.8rem 2rem;
    font-size: 1rem;
  }

  .text-container {
    padding: 0 1rem;
  }
}
</style>
