{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 3281106012824027394, "deps": [[654232091421095663, "tauri_utils", false, 16061983275688939768], [2704937418414716471, "tauri_codegen", false, 11068238717131615720], [3060637413840920116, "proc_macro2", false, 3196051902200663950], [4974441333307933176, "syn", false, 12573928928994442457], [13077543566650298139, "heck", false, 10697231594949197088], [17990358020177143287, "quote", false, 2963802732501907807]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-971143b6918fc977\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}