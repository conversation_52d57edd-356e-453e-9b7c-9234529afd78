<script setup>
import { ref } from "vue";
import WelcomeGuide from "@/components/WelcomeGuide.vue";

const showMainApp = ref(false);

// 处理引导完成事件
const handleGuideCompleted = () => {
  showMainApp.value = true;
}
</script>

<template>
  <!-- 引导页面 -->
  <WelcomeGuide @guide-completed="handleGuideCompleted" />

  <!-- 主应用内容 -->
  <div v-show="showMainApp">
    <router-view />
  </div>
</template>

<style>

</style>
