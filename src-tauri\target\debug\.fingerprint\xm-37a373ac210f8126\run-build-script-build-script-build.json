{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12092653563678505622, "build_script_build", false, 2168613222266226633], [12504415026414629397, "build_script_build", false, 11389383867724396997], [14909000976169095833, "build_script_build", false, 5796491446307976521], [16702348383442838006, "build_script_build", false, 7279255223541088279], [4972584477725338812, "build_script_build", false, 7340151915156326578], [6800327204153422865, "build_script_build", false, 4517177578861204681]], "local": [{"RerunIfChanged": {"output": "debug\\build\\xm-37a373ac210f8126\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}