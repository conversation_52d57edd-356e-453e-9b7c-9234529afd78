{"rustc": 1842507548689473721, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1677134433092527515, "path": 1931133501181233609, "deps": [[1542112352204983347, "build_script_build", false, 6336469505727044693], [2883436298747778685, "pki_types", false, 12426637349468929886], [3722963349756955755, "once_cell", false, 123780520211584151], [5491919304041016563, "ring", false, 5209494171269021770], [6528079939221783635, "zeroize", false, 7090665809362999628], [8151164558401866693, "<PERSON><PERSON><PERSON>", false, 10620489233773682171], [17003143334332120809, "subtle", false, 17058434260151109862]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-41a39a9e9b3b2a5c\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}